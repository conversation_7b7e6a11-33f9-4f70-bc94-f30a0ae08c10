"""
libvirt快照管理服务
提供快照的创建、删除、恢复等功能
"""

import libvirt
import xml.etree.ElementTree as ET
from datetime import datetime, timezone
from django.db import transaction
from django.contrib.auth.models import User
from .models import Domain, Snapshot, SnapshotDisk, SnapshotMemory, SnapshotOperation, SnapshotMetadata


class SnapshotService:
    """快照管理服务类"""
    
    def __init__(self, libvirt_uri='qemu:///system'):
        self.conn = libvirt.open(libvirt_uri)
        if self.conn is None:
            raise Exception('Failed to open connection to libvirt')
    
    def create_snapshot(self, domain_uuid, snapshot_name, description='', 
                       snapshot_type='full', disk_only=False, user=None):
        """
        创建虚拟机快照
        
        Args:
            domain_uuid: 虚拟机UUID
            snapshot_name: 快照名称
            description: 快照描述
            snapshot_type: 快照类型 ('full', 'disk', 'memory')
            disk_only: 是否仅创建磁盘快照
            user: 操作用户
        
        Returns:
            Snapshot对象
        """
        try:
            # 获取域对象
            domain_obj = Domain.objects.get(uuid=domain_uuid)
            libvirt_domain = self.conn.lookupByUUIDString(str(domain_uuid))
            
            # 创建快照操作记录
            operation = SnapshotOperation.objects.create(
                snapshot=None,  # 稍后更新
                operation_type='create',
                status='running',
                operator=user,
                operation_params={
                    'snapshot_name': snapshot_name,
                    'description': description,
                    'snapshot_type': snapshot_type,
                    'disk_only': disk_only
                }
            )
            
            # 构建快照XML
            snapshot_xml = self._build_snapshot_xml(
                snapshot_name, description, disk_only, libvirt_domain
            )
            
            # 设置创建标志
            flags = 0
            if disk_only:
                flags |= libvirt.VIR_DOMAIN_SNAPSHOT_CREATE_DISK_ONLY
            
            start_time = datetime.now()
            
            # 调用libvirt创建快照
            libvirt_snapshot = libvirt_domain.snapshotCreateXML(snapshot_xml, flags)
            
            end_time = datetime.now()
            creation_duration = (end_time - start_time).total_seconds()
            
            # 获取快照详细信息
            snapshot_xml_desc = libvirt_snapshot.getXMLDesc(0)
            snapshot_info = self._parse_snapshot_xml(snapshot_xml_desc)
            
            with transaction.atomic():
                # 创建快照记录
                snapshot = Snapshot.objects.create(
                    name=snapshot_name,
                    domain=domain_obj,
                    parent=self._get_current_snapshot(domain_obj),
                    description=description,
                    snapshot_type=snapshot_type,
                    state=snapshot_info['state'],
                    creation_time=datetime.fromtimestamp(
                        snapshot_info['creation_time'], tz=timezone.utc
                    ),
                    is_current=True,
                    libvirt_xml=snapshot_xml_desc,
                    domain_xml=snapshot_info['domain_xml'],
                    created_by=user
                )
                
                # 更新之前的当前快照
                Snapshot.objects.filter(
                    domain=domain_obj, is_current=True
                ).exclude(uuid=snapshot.uuid).update(is_current=False)
                
                # 创建内存快照记录
                memory_info = snapshot_info.get('memory', {})
                SnapshotMemory.objects.create(
                    snapshot=snapshot,
                    memory_type=memory_info.get('type', 'no'),
                    file_path=memory_info.get('file', ''),
                    file_size=memory_info.get('size')
                )
                
                # 创建磁盘快照记录
                for disk_info in snapshot_info.get('disks', []):
                    SnapshotDisk.objects.create(
                        snapshot=snapshot,
                        disk_name=disk_info['name'],
                        snapshot_type=disk_info['snapshot_type'],
                        disk_type=disk_info.get('type', 'file'),
                        source_file=disk_info['source_file'],
                        source_format=disk_info.get('source_format', ''),
                        snapshot_file=disk_info.get('snapshot_file', ''),
                        snapshot_format=disk_info.get('snapshot_format', ''),
                        source_size=disk_info.get('source_size'),
                        snapshot_size=disk_info.get('snapshot_size')
                    )
                
                # 创建元数据记录
                SnapshotMetadata.objects.create(
                    snapshot=snapshot,
                    creation_duration=creation_duration,
                    total_disk_size=sum(
                        d.get('source_size', 0) or 0 
                        for d in snapshot_info.get('disks', [])
                    ),
                    chain_depth=self._calculate_chain_depth(snapshot)
                )
                
                # 更新操作记录
                operation.snapshot = snapshot
                operation.status = 'completed'
                operation.completed_at = datetime.now()
                operation.save()
            
            return snapshot
            
        except Exception as e:
            # 更新操作记录为失败
            if 'operation' in locals():
                operation.status = 'failed'
                operation.error_message = str(e)
                operation.completed_at = datetime.now()
                operation.save()
            raise
    
    def delete_snapshot(self, snapshot_uuid, user=None):
        """删除快照"""
        try:
            snapshot = Snapshot.objects.get(uuid=snapshot_uuid)
            libvirt_domain = self.conn.lookupByUUIDString(str(snapshot.domain.uuid))
            
            # 创建删除操作记录
            operation = SnapshotOperation.objects.create(
                snapshot=snapshot,
                operation_type='delete',
                status='running',
                operator=user
            )
            
            # 获取libvirt快照对象
            libvirt_snapshot = libvirt_domain.snapshotLookupByName(snapshot.name, 0)
            
            # 删除快照
            libvirt_snapshot.delete(0)
            
            # 更新数据库记录
            with transaction.atomic():
                snapshot.is_active = False
                snapshot.save()
                
                # 标记磁盘删除进行中
                snapshot.disks.update(delete_in_progress=True)
                
                operation.status = 'completed'
                operation.completed_at = datetime.now()
                operation.save()
            
            return True
            
        except Exception as e:
            if 'operation' in locals():
                operation.status = 'failed'
                operation.error_message = str(e)
                operation.completed_at = datetime.now()
                operation.save()
            raise
    
    def revert_to_snapshot(self, snapshot_uuid, user=None):
        """恢复到指定快照"""
        try:
            snapshot = Snapshot.objects.get(uuid=snapshot_uuid)
            libvirt_domain = self.conn.lookupByUUIDString(str(snapshot.domain.uuid))
            
            # 创建恢复操作记录
            operation = SnapshotOperation.objects.create(
                snapshot=snapshot,
                operation_type='revert',
                status='running',
                operator=user
            )
            
            # 获取libvirt快照对象
            libvirt_snapshot = libvirt_domain.snapshotLookupByName(snapshot.name, 0)
            
            # 恢复快照
            libvirt_domain.revertToSnapshot(libvirt_snapshot, 0)
            
            # 更新当前快照
            with transaction.atomic():
                Snapshot.objects.filter(
                    domain=snapshot.domain, is_current=True
                ).update(is_current=False)
                
                snapshot.is_current = True
                snapshot.save()
                
                operation.status = 'completed'
                operation.completed_at = datetime.now()
                operation.save()
            
            return True
            
        except Exception as e:
            if 'operation' in locals():
                operation.status = 'failed'
                operation.error_message = str(e)
                operation.completed_at = datetime.now()
                operation.save()
            raise
    
    def _build_snapshot_xml(self, name, description, disk_only, domain):
        """构建快照XML"""
        root = ET.Element('domainsnapshot')
        
        # 基本信息
        name_elem = ET.SubElement(root, 'name')
        name_elem.text = name
        
        if description:
            desc_elem = ET.SubElement(root, 'description')
            desc_elem.text = description
        
        # 内存配置
        if disk_only:
            memory_elem = ET.SubElement(root, 'memory')
            memory_elem.set('snapshot', 'no')
        
        return ET.tostring(root, encoding='unicode')
    
    def _parse_snapshot_xml(self, xml_desc):
        """解析快照XML获取详细信息"""
        root = ET.fromstring(xml_desc)
        
        info = {
            'state': root.find('state').text if root.find('state') is not None else '',
            'creation_time': int(root.find('creationTime').text) if root.find('creationTime') is not None else 0,
            'domain_xml': ET.tostring(root.find('domain'), encoding='unicode') if root.find('domain') is not None else '',
            'memory': {},
            'disks': []
        }
        
        # 解析内存信息
        memory_elem = root.find('memory')
        if memory_elem is not None:
            info['memory'] = {
                'type': memory_elem.get('snapshot', 'no'),
                'file': memory_elem.get('file', ''),
            }
        
        # 解析磁盘信息
        disks_elem = root.find('disks')
        if disks_elem is not None:
            for disk_elem in disks_elem.findall('disk'):
                disk_info = {
                    'name': disk_elem.get('name'),
                    'snapshot_type': disk_elem.get('snapshot', 'no'),
                    'type': disk_elem.get('type', 'file'),
                    'source_file': '',
                    'snapshot_file': ''
                }
                
                source_elem = disk_elem.find('source')
                if source_elem is not None:
                    disk_info['snapshot_file'] = source_elem.get('file', '')
                
                driver_elem = disk_elem.find('driver')
                if driver_elem is not None:
                    disk_info['snapshot_format'] = driver_elem.get('type', '')
                
                info['disks'].append(disk_info)
        
        return info
    
    def _get_current_snapshot(self, domain):
        """获取当前快照"""
        return Snapshot.objects.filter(
            domain=domain, is_current=True
        ).first()
    
    def _calculate_chain_depth(self, snapshot):
        """计算快照链深度"""
        depth = 0
        current = snapshot.parent
        while current:
            depth += 1
            current = current.parent
        return depth
    
    def __del__(self):
        if hasattr(self, 'conn') and self.conn:
            self.conn.close()
