# Generated migration for libvirt snapshot models

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Domain',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True)),
                ('hypervisor_type', models.CharField(default='qemu', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'domains',
            },
        ),
        migrations.CreateModel(
            name='Snapshot',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('snapshot_type', models.CharField(choices=[('full', '完整系统快照'), ('disk', '磁盘快照'), ('memory', '内存快照')], default='full', max_length=20)),
                ('state', models.CharField(choices=[('running', '运行中'), ('paused', '暂停'), ('shutoff', '关闭'), ('disk-snapshot', '仅磁盘快照')], max_length=20)),
                ('creation_time', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_current', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('libvirt_xml', models.TextField()),
                ('domain_xml', models.TextField()),
                ('tags', models.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='auth.user')),
                ('domain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='snapshots', to='yourapp.domain')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='yourapp.snapshot')),
            ],
            options={
                'db_table': 'snapshots',
                'indexes': [
                    models.Index(fields=['domain', 'creation_time'], name='snapshots_domain_creation_idx'),
                    models.Index(fields=['domain', 'is_current'], name='snapshots_domain_current_idx'),
                    models.Index(fields=['parent'], name='snapshots_parent_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='SnapshotMemory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('memory_type', models.CharField(choices=[('internal', '内部存储'), ('external', '外部文件'), ('no', '无内存快照')], default='no', max_length=20)),
                ('file_path', models.CharField(blank=True, max_length=500)),
                ('file_size', models.BigIntegerField(blank=True, null=True)),
                ('snapshot', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='memory', to='yourapp.snapshot')),
            ],
            options={
                'db_table': 'snapshot_memory',
            },
        ),
        migrations.CreateModel(
            name='SnapshotDisk',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('disk_name', models.CharField(max_length=100)),
                ('snapshot_type', models.CharField(choices=[('internal', '内部快照'), ('external', '外部快照'), ('no', '不包含此磁盘'), ('manual', '手动快照')], max_length=20)),
                ('disk_type', models.CharField(choices=[('file', '文件'), ('block', '块设备'), ('network', '网络存储')], default='file', max_length=20)),
                ('source_file', models.CharField(max_length=500)),
                ('source_format', models.CharField(max_length=50)),
                ('snapshot_file', models.CharField(blank=True, max_length=500)),
                ('snapshot_format', models.CharField(blank=True, max_length=50)),
                ('source_size', models.BigIntegerField(blank=True, null=True)),
                ('snapshot_size', models.BigIntegerField(blank=True, null=True)),
                ('delete_in_progress', models.BooleanField(default=False)),
                ('snapshot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disks', to='yourapp.snapshot')),
            ],
            options={
                'db_table': 'snapshot_disks',
            },
        ),
        migrations.CreateModel(
            name='SnapshotOperation',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('operation_type', models.CharField(choices=[('create', '创建'), ('delete', '删除'), ('revert', '恢复'), ('merge', '合并')], max_length=20)),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '执行中'), ('completed', '完成'), ('failed', '失败'), ('cancelled', '取消')], default='pending', max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('operation_params', models.JSONField(blank=True, default=dict)),
                ('operator', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='auth.user')),
                ('snapshot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='yourapp.snapshot')),
            ],
            options={
                'db_table': 'snapshot_operations',
                'indexes': [
                    models.Index(fields=['snapshot', 'started_at'], name='snapshot_ops_snapshot_time_idx'),
                    models.Index(fields=['status'], name='snapshot_ops_status_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='SnapshotMetadata',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creation_duration', models.FloatField(blank=True, null=True)),
                ('total_disk_size', models.BigIntegerField(blank=True, null=True)),
                ('chain_depth', models.IntegerField(default=0)),
                ('has_children', models.BooleanField(default=False)),
                ('is_scheduled', models.BooleanField(default=False)),
                ('schedule_policy', models.CharField(blank=True, max_length=100)),
                ('retention_days', models.IntegerField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('custom_fields', models.JSONField(blank=True, default=dict)),
                ('snapshot', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='metadata', to='yourapp.snapshot')),
            ],
            options={
                'db_table': 'snapshot_metadata',
            },
        ),
        migrations.AddConstraint(
            model_name='snapshotdisk',
            constraint=models.UniqueConstraint(fields=('snapshot', 'disk_name'), name='unique_snapshot_disk'),
        ),
        migrations.AddConstraint(
            model_name='snapshot',
            constraint=models.UniqueConstraint(fields=('domain', 'name'), name='unique_domain_snapshot_name'),
        ),
    ]
