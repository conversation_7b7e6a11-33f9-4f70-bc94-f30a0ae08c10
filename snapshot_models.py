"""
libvirt虚拟机快照数据表设计
支持完整的快照管理功能，包括层次结构、磁盘快照、内存快照等
"""

from django.db import models
from django.contrib.auth.models import User
import uuid


class Domain(models.Model):
    """虚拟机域模型"""
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True)
    hypervisor_type = models.CharField(max_length=50, default='qemu')  # qemu, xen, vmware等
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'domains'
        
    def __str__(self):
        return f"{self.name} ({self.uuid})"


class Snapshot(models.Model):
    """快照主表"""
    SNAPSHOT_STATES = [
        ('running', '运行中'),
        ('paused', '暂停'),
        ('shutoff', '关闭'),
        ('disk-snapshot', '仅磁盘快照'),
    ]
    
    SNAPSHOT_TYPES = [
        ('full', '完整系统快照'),
        ('disk', '磁盘快照'),
        ('memory', '内存快照'),
    ]
    
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=255)
    domain = models.ForeignKey(Domain, on_delete=models.CASCADE, related_name='snapshots')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')
    
    # 快照基本信息
    description = models.TextField(blank=True)
    snapshot_type = models.CharField(max_length=20, choices=SNAPSHOT_TYPES, default='full')
    state = models.CharField(max_length=20, choices=SNAPSHOT_STATES)
    
    # 时间信息
    creation_time = models.DateTimeField()  # libvirt的creationTime
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # 快照状态
    is_current = models.BooleanField(default=False)  # 是否为当前快照
    is_active = models.BooleanField(default=True)    # 快照是否有效
    
    # libvirt相关
    libvirt_xml = models.TextField()  # 完整的快照XML
    domain_xml = models.TextField()   # 快照时的域XML
    
    # 元数据
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    tags = models.JSONField(default=dict, blank=True)  # 自定义标签
    
    class Meta:
        db_table = 'snapshots'
        unique_together = [['domain', 'name']]
        indexes = [
            models.Index(fields=['domain', 'creation_time']),
            models.Index(fields=['domain', 'is_current']),
            models.Index(fields=['parent']),
        ]
        
    def __str__(self):
        return f"{self.domain.name}:{self.name}"


class SnapshotMemory(models.Model):
    """快照内存状态"""
    MEMORY_TYPES = [
        ('internal', '内部存储'),
        ('external', '外部文件'),
        ('no', '无内存快照'),
    ]
    
    snapshot = models.OneToOneField(Snapshot, on_delete=models.CASCADE, related_name='memory')
    memory_type = models.CharField(max_length=20, choices=MEMORY_TYPES, default='no')
    file_path = models.CharField(max_length=500, blank=True)  # 外部内存文件路径
    file_size = models.BigIntegerField(null=True, blank=True)  # 文件大小(字节)
    
    class Meta:
        db_table = 'snapshot_memory'


class SnapshotDisk(models.Model):
    """快照磁盘信息"""
    DISK_SNAPSHOT_TYPES = [
        ('internal', '内部快照'),
        ('external', '外部快照'),
        ('no', '不包含此磁盘'),
        ('manual', '手动快照'),
    ]
    
    DISK_TYPES = [
        ('file', '文件'),
        ('block', '块设备'),
        ('network', '网络存储'),
    ]
    
    snapshot = models.ForeignKey(Snapshot, on_delete=models.CASCADE, related_name='disks')
    disk_name = models.CharField(max_length=100)  # 磁盘设备名 (如 vda, sda)
    
    # 快照配置
    snapshot_type = models.CharField(max_length=20, choices=DISK_SNAPSHOT_TYPES)
    disk_type = models.CharField(max_length=20, choices=DISK_TYPES, default='file')
    
    # 源文件信息
    source_file = models.CharField(max_length=500)  # 原始磁盘文件路径
    source_format = models.CharField(max_length=50)  # 原始格式 (qcow2, raw等)
    
    # 快照文件信息
    snapshot_file = models.CharField(max_length=500, blank=True)  # 快照文件路径
    snapshot_format = models.CharField(max_length=50, blank=True)  # 快照格式
    
    # 文件大小信息
    source_size = models.BigIntegerField(null=True, blank=True)
    snapshot_size = models.BigIntegerField(null=True, blank=True)
    
    # 删除状态
    delete_in_progress = models.BooleanField(default=False)  # 删除进行中
    
    class Meta:
        db_table = 'snapshot_disks'
        unique_together = [['snapshot', 'disk_name']]


class SnapshotOperation(models.Model):
    """快照操作记录"""
    OPERATION_TYPES = [
        ('create', '创建'),
        ('delete', '删除'),
        ('revert', '恢复'),
        ('merge', '合并'),
    ]
    
    OPERATION_STATUS = [
        ('pending', '等待中'),
        ('running', '执行中'),
        ('completed', '完成'),
        ('failed', '失败'),
        ('cancelled', '取消'),
    ]
    
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4)
    snapshot = models.ForeignKey(Snapshot, on_delete=models.CASCADE, related_name='operations')
    operation_type = models.CharField(max_length=20, choices=OPERATION_TYPES)
    status = models.CharField(max_length=20, choices=OPERATION_STATUS, default='pending')
    
    # 操作详情
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    
    # 操作参数
    operation_params = models.JSONField(default=dict, blank=True)
    
    # 操作者
    operator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        db_table = 'snapshot_operations'
        indexes = [
            models.Index(fields=['snapshot', 'started_at']),
            models.Index(fields=['status']),
        ]


class SnapshotMetadata(models.Model):
    """快照元数据扩展"""
    snapshot = models.OneToOneField(Snapshot, on_delete=models.CASCADE, related_name='metadata')
    
    # 性能统计
    creation_duration = models.FloatField(null=True, blank=True)  # 创建耗时(秒)
    total_disk_size = models.BigIntegerField(null=True, blank=True)  # 总磁盘大小
    
    # 快照链信息
    chain_depth = models.IntegerField(default=0)  # 快照链深度
    has_children = models.BooleanField(default=False)  # 是否有子快照
    
    # 自动化信息
    is_scheduled = models.BooleanField(default=False)  # 是否为计划任务创建
    schedule_policy = models.CharField(max_length=100, blank=True)  # 调度策略
    retention_days = models.IntegerField(null=True, blank=True)  # 保留天数
    
    # 备注信息
    notes = models.TextField(blank=True)
    custom_fields = models.JSONField(default=dict, blank=True)  # 自定义字段
    
    class Meta:
        db_table = 'snapshot_metadata'
