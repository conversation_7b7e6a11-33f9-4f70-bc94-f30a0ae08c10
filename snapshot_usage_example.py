"""
libvirt快照管理使用示例
展示如何使用快照数据表和服务类
"""

from django.contrib.auth.models import User
from .models import Domain, Snapshot, SnapshotDisk, SnapshotOperation
from .snapshot_service import SnapshotService
from datetime import datetime, timed<PERSON><PERSON>


def example_usage():
    """快照管理使用示例"""
    
    # 初始化快照服务
    snapshot_service = SnapshotService()
    
    # 获取用户
    user = User.objects.get(username='admin')
    
    # 1. 创建完整系统快照
    print("=== 创建完整系统快照 ===")
    domain_uuid = "550e8400-e29b-41d4-a716-************"  # 示例UUID
    
    try:
        snapshot = snapshot_service.create_snapshot(
            domain_uuid=domain_uuid,
            snapshot_name="system_backup_20241208",
            description="系统完整备份，包含所有磁盘和内存状态",
            snapshot_type='full',
            disk_only=False,
            user=user
        )
        print(f"快照创建成功: {snapshot.name} (UUID: {snapshot.uuid})")
        
        # 查看快照详情
        print(f"快照状态: {snapshot.state}")
        print(f"创建时间: {snapshot.creation_time}")
        print(f"磁盘数量: {snapshot.disks.count()}")
        print(f"内存快照类型: {snapshot.memory.memory_type}")
        
    except Exception as e:
        print(f"快照创建失败: {e}")
    
    # 2. 创建仅磁盘快照
    print("\n=== 创建仅磁盘快照 ===")
    try:
        disk_snapshot = snapshot_service.create_snapshot(
            domain_uuid=domain_uuid,
            snapshot_name="disk_backup_20241208",
            description="仅磁盘数据备份",
            snapshot_type='disk',
            disk_only=True,
            user=user
        )
        print(f"磁盘快照创建成功: {disk_snapshot.name}")
        
    except Exception as e:
        print(f"磁盘快照创建失败: {e}")
    
    # 3. 查询快照信息
    print("\n=== 查询快照信息 ===")
    domain = Domain.objects.get(uuid=domain_uuid)
    
    # 获取所有快照
    all_snapshots = Snapshot.objects.filter(domain=domain, is_active=True)
    print(f"域 {domain.name} 共有 {all_snapshots.count()} 个活跃快照")
    
    # 获取当前快照
    current_snapshot = Snapshot.objects.filter(
        domain=domain, is_current=True
    ).first()
    if current_snapshot:
        print(f"当前快照: {current_snapshot.name}")
    
    # 获取快照树结构
    root_snapshots = Snapshot.objects.filter(
        domain=domain, parent=None, is_active=True
    )
    for root in root_snapshots:
        print_snapshot_tree(root, 0)
    
    # 4. 快照操作历史
    print("\n=== 快照操作历史 ===")
    recent_operations = SnapshotOperation.objects.filter(
        snapshot__domain=domain,
        started_at__gte=datetime.now() - timedelta(days=7)
    ).order_by('-started_at')[:10]
    
    for op in recent_operations:
        print(f"{op.started_at.strftime('%Y-%m-%d %H:%M:%S')} - "
              f"{op.get_operation_type_display()} - "
              f"{op.get_status_display()} - "
              f"{op.snapshot.name if op.snapshot else 'N/A'}")
    
    # 5. 恢复快照
    print("\n=== 恢复快照 ===")
    if current_snapshot and current_snapshot.parent:
        try:
            parent_snapshot = current_snapshot.parent
            result = snapshot_service.revert_to_snapshot(
                parent_snapshot.uuid, user=user
            )
            if result:
                print(f"成功恢复到快照: {parent_snapshot.name}")
        except Exception as e:
            print(f"快照恢复失败: {e}")
    
    # 6. 删除快照
    print("\n=== 删除快照 ===")
    # 删除最老的快照（示例）
    oldest_snapshot = Snapshot.objects.filter(
        domain=domain, is_active=True
    ).order_by('creation_time').first()
    
    if oldest_snapshot and not oldest_snapshot.is_current:
        try:
            result = snapshot_service.delete_snapshot(
                oldest_snapshot.uuid, user=user
            )
            if result:
                print(f"成功删除快照: {oldest_snapshot.name}")
        except Exception as e:
            print(f"快照删除失败: {e}")


def print_snapshot_tree(snapshot, level):
    """打印快照树结构"""
    indent = "  " * level
    current_mark = " (当前)" if snapshot.is_current else ""
    print(f"{indent}- {snapshot.name}{current_mark} "
          f"[{snapshot.creation_time.strftime('%Y-%m-%d %H:%M')}]")
    
    # 递归打印子快照
    for child in snapshot.children.filter(is_active=True):
        print_snapshot_tree(child, level + 1)


def get_snapshot_statistics(domain_uuid):
    """获取快照统计信息"""
    domain = Domain.objects.get(uuid=domain_uuid)
    
    stats = {
        'total_snapshots': Snapshot.objects.filter(
            domain=domain, is_active=True
        ).count(),
        'disk_snapshots': Snapshot.objects.filter(
            domain=domain, is_active=True, snapshot_type='disk'
        ).count(),
        'full_snapshots': Snapshot.objects.filter(
            domain=domain, is_active=True, snapshot_type='full'
        ).count(),
        'total_disk_size': 0,
        'average_creation_time': 0,
        'max_chain_depth': 0
    }
    
    # 计算总磁盘大小
    total_size = SnapshotDisk.objects.filter(
        snapshot__domain=domain,
        snapshot__is_active=True
    ).aggregate(
        total=models.Sum('source_size')
    )['total'] or 0
    stats['total_disk_size'] = total_size
    
    # 计算平均创建时间
    avg_duration = domain.snapshots.filter(
        is_active=True,
        metadata__creation_duration__isnull=False
    ).aggregate(
        avg=models.Avg('metadata__creation_duration')
    )['avg'] or 0
    stats['average_creation_time'] = avg_duration
    
    # 计算最大链深度
    max_depth = domain.snapshots.filter(
        is_active=True
    ).aggregate(
        max_depth=models.Max('metadata__chain_depth')
    )['max_depth'] or 0
    stats['max_chain_depth'] = max_depth
    
    return stats


def cleanup_old_snapshots(domain_uuid, retention_days=30):
    """清理过期快照"""
    domain = Domain.objects.get(uuid=domain_uuid)
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    
    old_snapshots = Snapshot.objects.filter(
        domain=domain,
        is_active=True,
        creation_time__lt=cutoff_date,
        is_current=False,  # 不删除当前快照
        children__isnull=True  # 不删除有子快照的快照
    )
    
    snapshot_service = SnapshotService()
    deleted_count = 0
    
    for snapshot in old_snapshots:
        try:
            snapshot_service.delete_snapshot(snapshot.uuid)
            deleted_count += 1
            print(f"已删除过期快照: {snapshot.name}")
        except Exception as e:
            print(f"删除快照 {snapshot.name} 失败: {e}")
    
    print(f"共删除 {deleted_count} 个过期快照")
    return deleted_count


if __name__ == "__main__":
    # 运行示例
    example_usage()
    
    # 获取统计信息
    domain_uuid = "550e8400-e29b-41d4-a716-************"
    stats = get_snapshot_statistics(domain_uuid)
    print(f"\n=== 快照统计信息 ===")
    print(f"总快照数: {stats['total_snapshots']}")
    print(f"磁盘快照数: {stats['disk_snapshots']}")
    print(f"完整快照数: {stats['full_snapshots']}")
    print(f"总磁盘大小: {stats['total_disk_size'] / (1024**3):.2f} GB")
    print(f"平均创建时间: {stats['average_creation_time']:.2f} 秒")
    print(f"最大链深度: {stats['max_chain_depth']}")
    
    # 清理过期快照
    cleanup_old_snapshots(domain_uuid, retention_days=30)
